import { Injectable, Logger, OnModuleInit, Inject, forwardRef } from '@nestjs/common';
import { WarAutoHandler } from './war-auto.handler';
import { AutoActionService } from '../shared/auto-action.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class WarAutoService implements OnModuleInit {
  private readonly logger = new Logger(WarAutoService.name);

  constructor(
    private readonly warAutoHandler: WarAutoHandler,
    @Inject(forwardRef(() => AutoActionService))
    private readonly autoActionService: AutoActionService,
  ) {}

  /**
   * Register the war auto handler with the auto action service on module init
   */
  onModuleInit() {
    this.logger.log('Registering war auto handler');
    this.autoActionService.registerHandler(AutoMode.WAR, this.warAutoHandler);
  }

  /**
   * Start auto attack for a user in a war
   * @param userId User ID
   * @param warId War ID
   * @param energyPercentage Energy percentage (kept for API compatibility, not used in current implementation)
   */
  async startAutoAttack(
    userId: number,
    warId: string,
    energyPercentage: number, // eslint-disable-line @typescript-eslint/no-unused-vars
  ): Promise<void> {
    this.logger.log(`Starting auto attack for user ${userId} in war ${warId}`);

    // Start the auto action in the AutoActionService
    // This will update the user entity with auto mode settings
    await this.autoActionService.startAutoAction(userId, warId, AutoMode.WAR);

    this.logger.log(`Auto attack started for user ${userId} in war ${warId}`);
  }

  /**
   * Stop auto attack for a user in a war
   * @param userId User ID
   * @param warId War ID
   */
  async stopAutoAttack(userId: number, warId: string): Promise<void> {
    this.logger.log(`Stopping auto attack for user ${userId} in war ${warId}`);

    // Stop the auto action in the AutoActionService
    // This will also update the user entity to clear auto mode settings
    await this.autoActionService.stopAutoAction(userId, warId, AutoMode.WAR);

    this.logger.log(`Auto attack stopped for user ${userId} in war ${warId}`);
  }
}
