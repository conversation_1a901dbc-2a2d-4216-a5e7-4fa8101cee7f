import { Injectable, Inject, forwardRef, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { <PERSON>ronJob } from 'cron';
import { WarService } from './war.service';
import { UserService } from '../user/user.service';
import { WarStatus } from './entity/war.entity';
import { EnergyService } from '../user/energy.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';
import { AutoActionService } from 'src/shared/auto-action.service';

@Injectable()
export class WarAutoService {
  // private autoAttacks = new Map<string, CronJob>(); // userId_warId -> CronJob
  private readonly logger = new Logger(WarAutoService.name);

  constructor(
    @Inject(forwardRef(() => WarService))
    private warService: WarService,
    private userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    @Inject(forwardRef(() => AutoActionService))
    private autoActionService: AutoActionService
  ) {}

  async startAutoAttack(
    userId: number,
    warId: string,
    energyPercentage: number,
  ) {
    const user = await this.userService.findOne(userId);
    if (!user.isPremium) {
      throw new Error('Auto mode is only available for premium users');
    }

    const key = `${userId}_${warId}`;
    const autoAttacks = await this.autoActionService.getAutoActions();

    if (autoAttacks.has(key)) {
      throw new Error('Auto attack already running for this war');
    }

    // Execute the attack immediately with current energy
    try {
      // Get the user with updated energy
        await this.autoActionService.startAutoAction(user.id,warId, AutoMode.WAR);
    } catch (initialAttackError) {
      this.logger.error(
        `Initial auto attack execution failed for user ${userId} in war ${warId}: ${initialAttackError.message}`,
      );

      // If the error is not about insufficient energy, don't start the auto attack
      if (!initialAttackError.message.includes('Insufficient energy')) {
        throw initialAttackError;
      }
    }

    
}

  async stopAutoAttack(userId: number, warId: string) {
    const key = `${userId}_${warId}`;
    const autoAttacks = await this.autoActionService.getAutoActions();
    const job = autoAttacks.get(key);
    this.logger.log(`Stopping auto attack for user ${userId} in war ${warId}`);

    if (job) {
      this.autoActionService.stopAutoAction(userId,warId,AutoMode.WAR);
    } else {
      this.logger.warn(`No active auto attack job found for key: ${key}, but will still clean up user settings`);
    }

    try {
      // Always clear user's auto mode status, even if job wasn't found
      await this.userService.updateAutoMode(userId, {
        activeAutoMode: AutoMode.NONE,
        autoTargetId: null,
        autoModeExpiresAt: null
      });
      this.logger.log(`Cleared auto mode status for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error cleaning up auto mode for user ${userId}: ${error.message}`);
    }
  }
}
