import { Injectable, Logger, OnModuleInit, Inject, forwardRef } from '@nestjs/common';
import { WorkAutoHandler } from './work-auto.handler';
import { AutoActionService } from '../shared/auto-action.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class WorkAutoService implements OnModuleInit {
  private readonly logger = new Logger(WorkAutoService.name);

  constructor(
    private readonly workAutoHandler: WorkAutoHandler,
    @Inject(forwardRef(() => AutoActionService))
    private readonly autoActionService: AutoActionService,
  ) {}

  /**
   * Register the work auto handler with the auto action service on module init
   */
  onModuleInit() {
    this.logger.log('Registering work auto handler');
    this.autoActionService.registerHandler(AutoMode.WORK, this.workAutoHandler);
  }

  /**
   * Start auto work for a user at a factory
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async startAutoWork(userId: number, factoryId: string): Promise<void> {
    this.logger.log(`Starting auto work for user ${userId} at factory ${factoryId}`);

    // Start the auto action in the AutoActionService
    // This will update the user entity with auto mode settings
    await this.autoActionService.startAutoAction(userId, factoryId, AutoMode.WORK);

    this.logger.log(`Auto work started for user ${userId} at factory ${factoryId}`);
  }

  /**
   * Stop auto work for a user at a factory
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async stopAutoWork(userId: number, factoryId: string): Promise<void> {
    this.logger.log(`Stopping auto work for user ${userId} at factory ${factoryId}`);

    // Stop the auto action in the AutoActionService
    // This will also update the user entity to clear auto mode settings
    await this.autoActionService.stopAutoAction(userId, factoryId,AutoMode.WORK);

    this.logger.log(`Auto work stopped for user ${userId} at factory ${factoryId}`);
  }
}
