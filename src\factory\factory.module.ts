import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FactoryController } from './factory.controller';
import { FactoryService } from './factory.service';
import { Factory } from './entity/factory.entity';
import { User } from '../user/entity/user.entity';
import { WorkSession } from '../work-session/entity/work-session.entity';
import { UserModule } from 'src/user/user.module';
import { RegionModule } from 'src/region/region.module';
import { WorkAutoHandler } from './work-auto.handler';
import { WorkAutoService } from './work-auto.service';
import { SharedModule } from 'src/shared/shared.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Factory, User, WorkSession]),
    UserModule,
    RegionModule,
    forwardRef(() => SharedModule)
  ],
  controllers: [FactoryController],
  providers: [FactoryService, WorkAutoHandler, WorkAutoService],
  exports: [FactoryService, WorkAutoHandler, WorkAutoService],
})
export class FactoryModule {}
