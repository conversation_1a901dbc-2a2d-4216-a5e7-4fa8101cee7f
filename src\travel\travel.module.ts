import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TravelService } from './travel.service';
import { TravelController } from './travel.controller';
import { TravelAutoService } from './travel-auto.service';
import { TravelAutoHandler } from './travel-auto.handler';
import { Travel } from './entity/travel.entity';
import { TravelPermission } from './entity/travel-permission.entity';
import { User } from '../user/entity/user.entity';
import { Region } from '../region/entity/region.entity';
import { State } from '../state/entity/state.entity';
import { SharedModule } from '../shared/shared.module';
import { StateModule } from 'src/state/state.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Travel, TravelPermission, User, Region, State]),
    SharedModule,
    StateModule
  ],
  controllers: [TravelController],
  providers: [
    TravelService, 
    TravelAutoService, 
    TravelAutoHandler
  ],
  exports: [TravelService, TravelAutoService],
})
export class TravelModule {}

