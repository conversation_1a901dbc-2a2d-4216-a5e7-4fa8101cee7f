import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AutoActionHandler } from '../shared/auto-action.service';
import { Travel, TravelStatus } from './entity/travel.entity';
import { User } from '../user/entity/user.entity';

@Injectable()
export class TravelAutoHandler implements AutoActionHandler {
  private readonly logger = new Logger(TravelAutoHandler.name);
  private autoSettings = new Map<string, { userId: number; targetId: string }>();

  constructor(
    @InjectRepository(Travel)
    private travelRepository: Repository<Travel>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async executeAction(userId: number, travelId: string): Promise<void> {
    this.logger.log(`Executing travel completion for user ${userId}, travel ${travelId}`);
    
    try {
      const travel = await this.travelRepository.findOne({
        where: { id: travelId, status: TravelStatus.IN_PROGRESS },
        relations: ['user', 'destinationRegion'],
      });

      if (!travel) {
        this.logger.warn(`Travel not found or not in progress: ${travelId}`);
        return;
      }

      this.logger.log(`Found travel: ${JSON.stringify({
        id: travel.id,
        userId: travel.user?.id,
        destinationId: travel.destinationRegion?.id,
        status: travel.status
      })}`);

      // Complete the travel
      travel.status = TravelStatus.COMPLETED;
      await this.travelRepository.save(travel);
      this.logger.log(`Updated travel status to COMPLETED`);

      // Update user's region and traveling status
      const user = travel.user;
      if (!user) {
        this.logger.warn(`User not found for travel ${travelId}`);
        return;
      }

      user.region = travel.destinationRegion;
      user.isTraveling = false;
      await this.userRepository.save(user);
      this.logger.log(`Updated user ${user.id} region to ${travel.destinationRegion.id} and set isTraveling=false`);

      // Remove auto settings after completion
      await this.removeAutoSettings(userId, travelId);
    } catch (error) {
      this.logger.error(`Error executing travel completion: ${error.message}`, error.stack);
      throw error;
    }
  }

  async checkActionValidity(userId: number, travelId: string): Promise<boolean> {
    try {
      const travel = await this.travelRepository.findOne({
        where: { id: travelId, status: TravelStatus.IN_PROGRESS },
      });
      
      if (!travel) {
        this.logger.warn(`Travel not found or not in progress: ${travelId}`);
        return false;
      }
      
      // Check if travel has reached its end time
      const isValid = new Date() >= travel.endTime;
      this.logger.log(`Travel ${travelId} validity check: ${isValid}`);
      return isValid;
    } catch (error) {
      this.logger.error(`Error checking travel validity: ${error.message}`, error.stack);
      return false;
    }
  }

  async saveAutoSettings(userId: number, travelId: string): Promise<void> {
    const key = `${userId}_${travelId}`;
    this.autoSettings.set(key, { userId, targetId: travelId });
    this.logger.log(`Saved auto travel settings for user ${userId}, travel ${travelId}`);
  }

  async removeAutoSettings(userId: number, travelId: string): Promise<void> {
    const key = `${userId}_${travelId}`;
    this.autoSettings.delete(key);
    this.logger.log(`Removed auto travel settings for user ${userId}, travel ${travelId}`);
  }

  async getActiveAutoSettings(): Promise<{ userId: number; targetId: string }[]> {
    return Array.from(this.autoSettings.values());
  }
}
