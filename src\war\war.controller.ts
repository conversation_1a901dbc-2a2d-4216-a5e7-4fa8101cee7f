import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Request,
  HttpStatus,
  HttpCode,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { WarService } from './war.service';
import {
  WarAnalyticsService,
  WarStatistics,
  UserWarStatistics,
  WarTimelineEvent,
  RegionWarHistory,
} from './war-analytics.service';
import {
  WarAnalyticsAdvancedService,
  DamageLeaderboard,
  EfficiencyMetrics,
  RegionalPerformance,
  WarTrends,
} from './war-analytics-advanced.service';
import { WarHistoryService } from './war-history.service';
import { WarReportService } from './war-report.service';
import { WarHistory } from './entity/war-history.entity';
import { WarReport } from './entity/war-report.entity';
import { WarHistoryResponseDto } from './dto/war-history-response.dto';
import { WarReportResponseDto } from './dto/war-report-response.dto';
import { War } from './entity/war.entity';
import { CreateWarDto } from './dto/create-war.dto';
import { ParticipateInWarDto } from './dto/participate-in-war.dto';
import { Request as ExpressRequest } from 'express';

@ApiTags('Wars')
@Controller('wars')
export class WarController {
  constructor(
    private readonly warService: WarService,
    private readonly warAnalyticsService: WarAnalyticsService,
    private readonly warAnalyticsAdvancedService: WarAnalyticsAdvancedService,
    private readonly warHistoryService: WarHistoryService,
    private readonly warReportService: WarReportService,
  ) {}

  @ApiOperation({ summary: 'Get all wars' })
  @ApiResponse({
    status: 200,
    description: 'List of all wars',
    type: [War],
  })
  @Get()
  async findAllWars(): Promise<War[]> {
    return this.warService.findAllWars();
  }

  @ApiOperation({ summary: 'Get all active wars' })
  @ApiResponse({
    status: 200,
    description: 'List of all active wars',
    type: [War],
  })
  @Get('active')
  async findActiveWars(): Promise<War[]> {
    return this.warService.findActiveWars();
  }

  @ApiOperation({ summary: 'Get wars involving the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'List of wars involving the user',
    type: [War],
  })
  @Get('my-wars')
  async findMyWars(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<War[]> {
    return this.warService.findWarsByUser(req.user.userId);
  }

  @ApiOperation({ summary: 'Get war by ID' })
  @ApiParam({ name: 'id', description: 'War ID' })
  @ApiResponse({
    status: 200,
    description: 'War details',
    type: War,
  })
  @ApiResponse({
    status: 404,
    description: 'War not found',
  })
  @Get(':id')
  async findWarById(@Param('id') id: string): Promise<War> {
    return this.warService.findWarById(id);
  }

  @ApiOperation({ summary: 'Declare a new war' })
  @ApiResponse({
    status: 201,
    description: 'War has been declared',
    type: War,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid war declaration parameters',
  })
  @Post('declare')
  @HttpCode(HttpStatus.CREATED)
  async declareWar(
    @Request() req: ExpressRequest & { user: { userId: number } },
    @Body() createWarDto: CreateWarDto,
  ): Promise<War> {
    return this.warService.declareWar(req.user.userId, createWarDto);
  }

  @ApiOperation({ summary: 'Participate in an ongoing war' })
  @ApiParam({ name: 'id', description: 'War ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully participated in the war',
    type: War,
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot participate in this war',
  })
  @ApiResponse({
    status: 404,
    description: 'War not found',
  })
  @Post(':id/participate')
  @HttpCode(HttpStatus.OK)
  async participateInWar(
    @Request() req: ExpressRequest & { user: { userId: number } },
    @Param('id') id: string,
    @Body() participateDto: ParticipateInWarDto,
  ): Promise<War> {
    return this.warService.participateInWar(
      req.user.userId,
      id,
      participateDto,
    );
  }

  @ApiOperation({ summary: 'Get global war statistics' })
  @ApiResponse({
    status: 200,
    description: 'Global war statistics',
  })
  @Get('analytics/global')
  async getGlobalWarStatistics(): Promise<WarStatistics> {
    return this.warAnalyticsService.getGlobalWarStatistics();
  }

  @ApiOperation({ summary: 'Get war statistics for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'User war statistics',
  })
  @Get('analytics/user')
  async getUserWarStatistics(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<UserWarStatistics> {
    return this.warAnalyticsService.getUserWarStatistics(req.user.userId);
  }

  @ApiOperation({ summary: 'Get war timeline events' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of events to return',
  })
  @ApiResponse({
    status: 200,
    description: 'War timeline events',
  })
  @Get('analytics/timeline')
  async getWarTimeline(
    @Query('limit') limit?: number,
  ): Promise<WarTimelineEvent[]> {
    return this.warAnalyticsService.getWarTimeline(limit || 10);
  }

  @ApiOperation({ summary: 'Get war history for a specific region' })
  @ApiParam({ name: 'regionId', description: 'Region ID' })
  @ApiResponse({
    status: 200,
    description: 'Region war history',
  })
  @Get('analytics/region/:regionId')
  async getRegionWarHistory(
    @Param('regionId') regionId: string,
  ): Promise<RegionWarHistory> {
    return this.warAnalyticsService.getRegionWarHistory(regionId);
  }

  @ApiOperation({ summary: 'Get war history records' })
  @ApiResponse({
    status: 200,
    description: 'List of war history records',
    type: [WarHistoryResponseDto],
  })
  @Get('history')
  async getWarHistory(): Promise<WarHistory[]> {
    return this.warHistoryService.findAllWarHistory();
  }

  @ApiOperation({ summary: 'Get war history by ID' })
  @ApiParam({ name: 'id', description: 'War history ID' })
  @ApiResponse({
    status: 200,
    description: 'War history record',
    type: WarHistoryResponseDto,
  })
  @Get('history/:id')
  async getWarHistoryById(@Param('id') id: string): Promise<WarHistory> {
    return this.warHistoryService.findWarHistoryById(id);
  }

  @ApiOperation({ summary: 'Get war history statistics' })
  @ApiResponse({
    status: 200,
    description: 'War history statistics',
  })
  @Get('history/statistics')
  async getWarHistoryStatistics(): Promise<any> {
    return this.warHistoryService.getWarHistoryStatistics();
  }

  @ApiOperation({ summary: 'Get war history by region' })
  @ApiParam({ name: 'regionName', description: 'Region name' })
  @ApiResponse({
    status: 200,
    description: 'War history records for the region',
    type: [WarHistoryResponseDto],
  })
  @Get('history/region/:regionName')
  async getWarHistoryByRegion(
    @Param('regionName') regionName: string,
  ): Promise<WarHistory[]> {
    return this.warHistoryService.findWarHistoryByRegion(regionName);
  }

  @ApiOperation({ summary: 'Get war history by state' })
  @ApiParam({ name: 'stateName', description: 'State name' })
  @ApiResponse({
    status: 200,
    description: 'War history records for the state',
    type: [WarHistoryResponseDto],
  })
  @Get('history/state/:stateName')
  async getWarHistoryByState(
    @Param('stateName') stateName: string,
  ): Promise<WarHistory[]> {
    return this.warHistoryService.findWarHistoryByState(stateName);
  }

  @ApiOperation({ summary: 'Get war reports' })
  @ApiResponse({
    status: 200,
    description: 'List of war reports',
    type: [WarReportResponseDto],
  })
  @Get('reports')
  async getWarReports(): Promise<WarReport[]> {
    return this.warReportService.findAllWarReports();
  }

  @ApiOperation({ summary: 'Get war report by ID' })
  @ApiParam({ name: 'id', description: 'War report ID' })
  @ApiResponse({
    status: 200,
    description: 'War report',
    type: WarReportResponseDto,
  })
  @Get('reports/:id')
  async getWarReportById(@Param('id') id: string): Promise<WarReport> {
    return this.warReportService.findWarReportById(id);
  }

  @ApiOperation({ summary: 'Get war report by war ID' })
  @ApiParam({ name: 'warId', description: 'War ID' })
  @ApiResponse({
    status: 200,
    description: 'War report',
    type: WarReportResponseDto,
  })
  @Get('reports/war/:warId')
  async getWarReportByWarId(@Param('warId') warId: string): Promise<WarReport> {
    return this.warReportService.findWarReportByWarId(warId);
  }

  @ApiOperation({ summary: 'Get top warriors' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of warriors to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Top warriors',
  })
  @Get('reports/leaderboard/top-warriors')
  async getTopWarriors(@Query('limit') limit?: number): Promise<any[]> {
    return this.warReportService.getTopWarriors(limit || 10);
  }

  @ApiOperation({ summary: 'Get most efficient warriors' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of warriors to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Most efficient warriors',
  })
  @Get('reports/leaderboard/most-efficient')
  async getMostEfficientWarriors(
    @Query('limit') limit?: number,
  ): Promise<any[]> {
    return this.warReportService.getMostEfficientWarriors(limit || 10);
  }

  @ApiOperation({ summary: 'Get damage leaderboard' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of entries to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Damage leaderboard',
  })
  @Get('analytics/advanced/damage-leaderboard')
  async getDamageLeaderboard(
    @Query('limit') limit?: number,
  ): Promise<DamageLeaderboard[]> {
    return this.warAnalyticsAdvancedService.getDamageLeaderboard(limit || 10);
  }

  @ApiOperation({ summary: 'Get efficiency metrics' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Maximum number of entries to return',
  })
  @ApiResponse({
    status: 200,
    description: 'Efficiency metrics',
  })
  @Get('analytics/advanced/efficiency-metrics')
  async getEfficiencyMetrics(
    @Query('limit') limit?: number,
  ): Promise<EfficiencyMetrics[]> {
    return this.warAnalyticsAdvancedService.getEfficiencyMetrics(limit || 10);
  }

  @ApiOperation({ summary: 'Get regional performance' })
  @ApiParam({ name: 'regionId', description: 'Region ID' })
  @ApiResponse({
    status: 200,
    description: 'Regional performance metrics',
  })
  @Get('analytics/advanced/regional-performance/:regionId')
  async getRegionalPerformance(
    @Param('regionId') regionId: string,
  ): Promise<RegionalPerformance> {
    return this.warAnalyticsAdvancedService.getRegionalPerformance(regionId);
  }

  @ApiOperation({ summary: 'Get war trends' })
  @ApiResponse({
    status: 200,
    description: 'War trends over different timeframes',
  })
  @Get('analytics/advanced/trends')
  async getWarTrends(): Promise<WarTrends[]> {
    return this.warAnalyticsAdvancedService.getWarTrends();
  }
}
