/**
 * Utility functions for calculating distances and travel times between regions
 */

/**
 * Calculate the distance between two points using the Haversine formula
 * @param lat1 Latitude of point 1 in degrees
 * @param lon1 Longitude of point 1 in degrees
 * @param lat2 Latitude of point 2 in degrees
 * @param lon2 Longitude of point 2 in degrees
 * @returns Distance in kilometers
 */
export function calculateHaversineDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number {
  // Convert latitude and longitude from degrees to radians
  const toRadians = (degrees: number) => (degrees * Math.PI) / 180;
  const radLat1 = toRadians(lat1);
  const radLon1 = toRadians(lon1);
  const radLat2 = toRadians(lat2);
  const radLon2 = toRadians(lon2);

  // Haversine formula
  const dLat = radLat2 - radLat1;
  const dLon = radLon2 - radLon1;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(radLat1) *
      Math.cos(radLat2) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  // Earth's radius in kilometers
  const earthRadius = 6371;
  const distance = earthRadius * c;

  return distance;
}

/**
 * Calculate travel time between two points
 * @param distance Distance in kilometers
 * @param seaCrossing Whether the travel crosses a sea
 * @param sameState Whether the travel is within the same state
 * @returns Travel time in minutes
 */
export function calculateTravelTime(
  distance: number,
  seaCrossing: boolean,
  sameState: boolean,
): number {
  // Base travel speed in km/hour
  const baseSpeed = 200;

  // Convert distance to minutes (distance/baseSpeed * 60)
  let travelTime = (distance / baseSpeed) * 60;

  // Apply scaling factor to make game time reasonable
  travelTime = travelTime * 0.1;

  // Apply modifiers
  if (seaCrossing) {
    // Sea crossing: 1.5x travel time
    travelTime *= 1.5;
  }

  if (sameState) {
    // Same state travel: 0.8x travel time (20% faster)
    travelTime *= 0.8;
  }

  // Round to nearest minute
  return Math.round(travelTime);
}
