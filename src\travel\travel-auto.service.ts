import { Injectable, Logger, OnModuleInit, Inject, forwardRef } from '@nestjs/common';
import { TravelAutoHandler } from './travel-auto.handler';
import { AutoActionService } from '../shared/auto-action.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class TravelAutoService implements OnModuleInit {
  private readonly logger = new Logger(TravelAutoService.name);

  constructor(
    private readonly travelAutoHandler: TravelAutoHandler,
    @Inject(forwardRef(() => AutoActionService))
    private readonly autoActionService: AutoActionService,
  ) {}

  /**
   * Register the travel auto handler with the auto action service on module init
   */
  onModuleInit() {
    this.logger.log('Registering travel auto handler');
    this.autoActionService.registerHandler(AutoMode.TRAVEL, this.travelAutoHandler);
  }

  /**
   * Schedule travel completion
   */
  async scheduleTravelCompletion(userId: number, travelId: string, endTime: Date): Promise<void> {
    this.logger.log(`Scheduling travel completion for user ${userId}, travel ${travelId}`);
    
    // Save auto settings
    await this.travelAutoHandler.saveAutoSettings(userId, travelId);
    
    // Calculate time until completion
    const now = new Date();
    const timeUntilCompletion = Math.max(0, endTime.getTime() - now.getTime());
    
    this.logger.log(`Travel will complete in ${timeUntilCompletion}ms at ${endTime}`);
    
    // Schedule execution at the exact time
    setTimeout(async () => {
      try {
        this.logger.log(`Executing scheduled travel completion for user ${userId}, travel ${travelId}`);
        await this.autoActionService.startAutoAction(userId, travelId, AutoMode.TRAVEL);
      } catch (error) {
        this.logger.error(`Error in scheduled travel completion: ${error.message}`, error.stack);
        
        // Fallback: directly execute the action if auto service fails
        try {
          this.logger.log(`Attempting direct execution of travel completion`);
          await this.travelAutoHandler.executeAction(userId, travelId);
        } catch (fallbackError) {
          this.logger.error(`Fallback execution also failed: ${fallbackError.message}`, fallbackError.stack);
        }
      }
    }, timeUntilCompletion);
    
    this.logger.log(`Travel completion scheduled for ${endTime}`);
  }
}
